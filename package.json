{"name": "fumadocs-static-search", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@next/mdx": "^14.0.0", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "flexsearch": "^0.7.43", "lucide-react": "^0.292.0", "tailwindcss": "^3.3.0", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "typescript": "^5.0.0"}}