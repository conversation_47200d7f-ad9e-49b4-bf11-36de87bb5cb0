// This file is generated by Fumadocs, do not edit manually

export const map = [
  {
    type: 'page',
    file: './content/docs/index.mdx',
    url: '/docs',
    data: {
      title: '文档首页',
      description: '欢迎来到Fumadocs静态搜索文档',
    },
  },
  {
    type: 'page',
    file: './content/docs/getting-started.mdx',
    url: '/docs/getting-started',
    data: {
      title: '快速开始',
      description: '学习如何快速开始使用Fumadocs',
      icon: 'Rocket',
    },
  },
  {
    type: 'page',
    file: './content/docs/installation.mdx',
    url: '/docs/installation',
    data: {
      title: '安装指南',
      description: '详细的安装和配置指南',
      icon: 'Download',
    },
  },
  {
    type: 'page',
    file: './content/docs/configuration.mdx',
    url: '/docs/configuration',
    data: {
      title: '配置选项',
      description: '了解Fumadocs的各种配置选项',
      icon: 'Settings',
    },
  },
  {
    type: 'page',
    file: './content/docs/static-export.mdx',
    url: '/docs/static-export',
    data: {
      title: '静态导出',
      description: '学习如何将文档站点导出为静态文件',
      icon: 'Download',
    },
  },
  {
    type: 'page',
    file: './content/docs/search.mdx',
    url: '/docs/search',
    data: {
      title: '搜索功能',
      description: '了解如何使用和自定义搜索功能',
      icon: 'Search',
    },
  },
] as const;
