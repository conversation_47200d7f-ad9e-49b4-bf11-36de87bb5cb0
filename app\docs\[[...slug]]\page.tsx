import { notFound } from 'next/navigation';
import { getPage, getPages } from '@/app/source';
import type { Metadata } from 'next';
import dynamic from 'next/dynamic';

export default async function Page({
  params,
}: {
  params: { slug?: string[] };
}) {
  const page = getPage(params.slug);

  if (page == null) {
    notFound();
  }

  const MDXComponent = dynamic(page.data.body, {
    loading: () => <div>加载中...</div>,
  });

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <article className="prose prose-lg max-w-none">
        <MDXComponent />
      </article>
    </div>
  );
}

export async function generateStaticParams() {
  return getPages().map((page) => ({
    slug: page.slugs,
  }));
}

export function generateMetadata({
  params,
}: {
  params: { slug?: string[] };
}): Metadata {
  const page = getPage(params.slug);

  if (page == null) notFound();

  return {
    title: page.data.title,
    description: page.data.description,
  };
}
