"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-ui";
exports.ids = ["vendor-chunks/fumadocs-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/chunk-27HFSL7N.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/chunk-27HFSL7N.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   useSidebar: () => (/* binding */ useSidebar)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var fumadocs_core_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fumadocs-core/sidebar */ \"(ssr)/./node_modules/fumadocs-core/dist/sidebar.js\");\n/* harmony import */ var fumadocs_core_utils_use_on_change__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fumadocs-core/utils/use-on-change */ \"(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/contexts/sidebar.tsx\n\n\n\n\n\nvar SidebarContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nfunction useSidebar() {\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SidebarContext);\n  if (!ctx) throw new Error(\"Missing root provider\");\n  return ctx;\n}\nfunction SidebarProvider({\n  children\n}) {\n  const closeOnRedirect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n  (0,fumadocs_core_utils_use_on_change__WEBPACK_IMPORTED_MODULE_3__.useOnChange)(pathname, () => {\n    if (closeOnRedirect.current) {\n      setOpen(false);\n    }\n    closeOnRedirect.current = true;\n  });\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\n    SidebarContext.Provider,\n    {\n      value: (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n        () => ({\n          open,\n          setOpen,\n          collapsed,\n          setCollapsed,\n          closeOnRedirect\n        }),\n        [open, collapsed]\n      ),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(fumadocs_core_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarProvider, { open, onOpenChange: setOpen, children })\n    }\n  );\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/chunk-27HFSL7N.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/chunk-EFMHXXHW.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/chunk-EFMHXXHW.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* binding */ I18nContext),\n/* harmony export */   useI18n: () => (/* binding */ useI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/contexts/i18n.tsx\n\nvar I18nContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  text: {\n    search: \"Search\",\n    searchNoResult: \"No results found\",\n    toc: \"On this page\",\n    tocNoHeadings: \"No Headings\",\n    lastUpdate: \"Last updated on\",\n    chooseLanguage: \"Choose a language\",\n    nextPage: \"Next\",\n    previousPage: \"Previous\",\n    chooseTheme: \"Theme\",\n    editOnGithub: \"Edit on GitHub\"\n  }\n});\nfunction useI18n() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(I18nContext);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jaHVuay1FRk1IWFhIVy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNrRDtBQUNsRCxrQkFBa0Isb0RBQWE7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsU0FBUyxpREFBVTtBQUNuQjs7QUFLRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1bWFkb2NzLXN0YXRpYy1zZWFyY2gvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jaHVuay1FRk1IWFhIVy5qcz83NDdhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb250ZXh0cy9pMThuLnRzeFxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCB9IGZyb20gXCJyZWFjdFwiO1xudmFyIEkxOG5Db250ZXh0ID0gY3JlYXRlQ29udGV4dCh7XG4gIHRleHQ6IHtcbiAgICBzZWFyY2g6IFwiU2VhcmNoXCIsXG4gICAgc2VhcmNoTm9SZXN1bHQ6IFwiTm8gcmVzdWx0cyBmb3VuZFwiLFxuICAgIHRvYzogXCJPbiB0aGlzIHBhZ2VcIixcbiAgICB0b2NOb0hlYWRpbmdzOiBcIk5vIEhlYWRpbmdzXCIsXG4gICAgbGFzdFVwZGF0ZTogXCJMYXN0IHVwZGF0ZWQgb25cIixcbiAgICBjaG9vc2VMYW5ndWFnZTogXCJDaG9vc2UgYSBsYW5ndWFnZVwiLFxuICAgIG5leHRQYWdlOiBcIk5leHRcIixcbiAgICBwcmV2aW91c1BhZ2U6IFwiUHJldmlvdXNcIixcbiAgICBjaG9vc2VUaGVtZTogXCJUaGVtZVwiLFxuICAgIGVkaXRPbkdpdGh1YjogXCJFZGl0IG9uIEdpdEh1YlwiXG4gIH1cbn0pO1xuZnVuY3Rpb24gdXNlSTE4bigpIHtcbiAgcmV0dXJuIHVzZUNvbnRleHQoSTE4bkNvbnRleHQpO1xufVxuXG5leHBvcnQge1xuICBJMThuQ29udGV4dCxcbiAgdXNlSTE4blxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/chunk-EFMHXXHW.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/chunk-ET4TW6M5.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/chunk-ET4TW6M5.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchProvider: () => (/* binding */ SearchProvider),\n/* harmony export */   useSearchContext: () => (/* binding */ useSearchContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/contexts/search.tsx\n\n\nvar SearchContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  enabled: false,\n  hotKey: [],\n  setOpenSearch: () => void 0\n});\nfunction useSearchContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SearchContext);\n}\nfunction SearchProvider({\n  SearchDialog,\n  children,\n  preload = true,\n  options,\n  hotKey = [\n    {\n      key: (e) => e.metaKey || e.ctrlKey,\n      display: \"\\u2318\"\n    },\n    {\n      key: \"k\",\n      display: \"K\"\n    }\n  ],\n  links\n}) {\n  const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(preload ? false : void 0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const handler = (e) => {\n      if (hotKey.every(\n        (v) => typeof v.key === \"string\" ? e.key === v.key : v.key(e)\n      )) {\n        setIsOpen(true);\n        e.preventDefault();\n      }\n    };\n    window.addEventListener(\"keydown\", handler);\n    return () => {\n      window.removeEventListener(\"keydown\", handler);\n    };\n  }, [hotKey]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n    SearchContext.Provider,\n    {\n      value: (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n        () => ({ enabled: true, hotKey, setOpenSearch: setIsOpen }),\n        [hotKey]\n      ),\n      children: [\n        isOpen !== void 0 && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          SearchDialog,\n          {\n            open: isOpen,\n            onOpenChange: setIsOpen,\n            links,\n            ...options\n          }\n        ),\n        children\n      ]\n    }\n  );\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/chunk-ET4TW6M5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/chunk-LSTPTAZ5.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/chunk-LSTPTAZ5.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeContextProvider: () => (/* binding */ TreeContextProvider),\n/* harmony export */   useTreeContext: () => (/* binding */ useTreeContext)\n/* harmony export */ });\n/* harmony import */ var _chunk_UOD2T27N_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-UOD2T27N.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-UOD2T27N.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/contexts/tree.tsx\n\n\n\nvar TreeContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(void 0);\nfunction findRoot(items, pathname) {\n  for (const item of items) {\n    if (item.type === \"folder\") {\n      const root = findRoot(item.children, pathname);\n      if (root) return root;\n      if (item.root === true && (0,_chunk_UOD2T27N_js__WEBPACK_IMPORTED_MODULE_0__.hasActive)(item.children, pathname)) {\n        return item;\n      }\n    }\n  }\n}\nfunction getNavigationList(tree) {\n  return tree.flatMap((node) => {\n    if (node.type === \"separator\") return [];\n    if (node.type === \"folder\") {\n      const children = getNavigationList(node.children);\n      if (!node.root && node.index) children.unshift(node.index);\n      return children;\n    }\n    return !node.external ? [node] : [];\n  });\n}\nfunction TreeContextProvider({\n  children,\n  tree\n}) {\n  const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(() => {\n    const root = findRoot(tree.children, pathname) ?? tree;\n    const navigation = getNavigationList(root.children);\n    return {\n      root,\n      navigation,\n      tree\n    };\n  }, [pathname, tree]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TreeContext.Provider, { value, children });\n}\nfunction useTreeContext() {\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(TreeContext);\n  if (!ctx)\n    throw new Error(\"You must wrap this component under <DocsLayout />\");\n  return ctx;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/chunk-LSTPTAZ5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/chunk-MLKGABMK.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/chunk-MLKGABMK.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __export: () => (/* binding */ __export)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jaHVuay1NTEtHQUJNSy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLGtDQUFrQztBQUNoRTs7QUFJRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1bWFkb2NzLXN0YXRpYy1zZWFyY2gvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jaHVuay1NTEtHQUJNSy5qcz85Yzk1Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBfX2RlZlByb3AgPSBPYmplY3QuZGVmaW5lUHJvcGVydHk7XG52YXIgX19leHBvcnQgPSAodGFyZ2V0LCBhbGwpID0+IHtcbiAgZm9yICh2YXIgbmFtZSBpbiBhbGwpXG4gICAgX19kZWZQcm9wKHRhcmdldCwgbmFtZSwgeyBnZXQ6IGFsbFtuYW1lXSwgZW51bWVyYWJsZTogdHJ1ZSB9KTtcbn07XG5cbmV4cG9ydCB7XG4gIF9fZXhwb3J0XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/chunk-MLKGABMK.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/chunk-UOD2T27N.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/chunk-UOD2T27N.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultImageSizes: () => (/* binding */ defaultImageSizes),\n/* harmony export */   hasActive: () => (/* binding */ hasActive),\n/* harmony export */   isActive: () => (/* binding */ isActive),\n/* harmony export */   replaceOrDefault: () => (/* binding */ replaceOrDefault)\n/* harmony export */ });\n// src/utils/shared.ts\nvar defaultImageSizes = \"(max-width: 768px) 100vw, (max-width: 1200px) 70vw, 900px\";\nfunction isActive(url, pathname, nested = true) {\n  return url === pathname || nested && pathname.startsWith(`${url}/`);\n}\nfunction replaceOrDefault(obj, def) {\n  if (obj?.enabled === false) return;\n  if (obj?.component !== void 0) return obj.component;\n  return def;\n}\nfunction hasActive(items, url) {\n  return items.some((item) => {\n    if (item.type === \"page\") {\n      return item.url === url;\n    }\n    if (item.type === \"folder\")\n      return item.index?.url === url || hasActive(item.children, url);\n    return false;\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jaHVuay1VT0QyVDI3Ti5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsOERBQThELElBQUk7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFPRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1bWFkb2NzLXN0YXRpYy1zZWFyY2gvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jaHVuay1VT0QyVDI3Ti5qcz84YzcyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9zaGFyZWQudHNcbnZhciBkZWZhdWx0SW1hZ2VTaXplcyA9IFwiKG1heC13aWR0aDogNzY4cHgpIDEwMHZ3LCAobWF4LXdpZHRoOiAxMjAwcHgpIDcwdncsIDkwMHB4XCI7XG5mdW5jdGlvbiBpc0FjdGl2ZSh1cmwsIHBhdGhuYW1lLCBuZXN0ZWQgPSB0cnVlKSB7XG4gIHJldHVybiB1cmwgPT09IHBhdGhuYW1lIHx8IG5lc3RlZCAmJiBwYXRobmFtZS5zdGFydHNXaXRoKGAke3VybH0vYCk7XG59XG5mdW5jdGlvbiByZXBsYWNlT3JEZWZhdWx0KG9iaiwgZGVmKSB7XG4gIGlmIChvYmo/LmVuYWJsZWQgPT09IGZhbHNlKSByZXR1cm47XG4gIGlmIChvYmo/LmNvbXBvbmVudCAhPT0gdm9pZCAwKSByZXR1cm4gb2JqLmNvbXBvbmVudDtcbiAgcmV0dXJuIGRlZjtcbn1cbmZ1bmN0aW9uIGhhc0FjdGl2ZShpdGVtcywgdXJsKSB7XG4gIHJldHVybiBpdGVtcy5zb21lKChpdGVtKSA9PiB7XG4gICAgaWYgKGl0ZW0udHlwZSA9PT0gXCJwYWdlXCIpIHtcbiAgICAgIHJldHVybiBpdGVtLnVybCA9PT0gdXJsO1xuICAgIH1cbiAgICBpZiAoaXRlbS50eXBlID09PSBcImZvbGRlclwiKVxuICAgICAgcmV0dXJuIGl0ZW0uaW5kZXg/LnVybCA9PT0gdXJsIHx8IGhhc0FjdGl2ZShpdGVtLmNoaWxkcmVuLCB1cmwpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfSk7XG59XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRJbWFnZVNpemVzLFxuICBpc0FjdGl2ZSxcbiAgcmVwbGFjZU9yRGVmYXVsdCxcbiAgaGFzQWN0aXZlXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/chunk-UOD2T27N.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-ui/dist/provider.js":
/*!***************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/provider.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RootProvider: () => (/* binding */ RootProvider),\n/* harmony export */   useI18n: () => (/* reexport safe */ _chunk_EFMHXXHW_js__WEBPACK_IMPORTED_MODULE_4__.useI18n),\n/* harmony export */   useSearchContext: () => (/* reexport safe */ _chunk_ET4TW6M5_js__WEBPACK_IMPORTED_MODULE_2__.useSearchContext),\n/* harmony export */   useSidebar: () => (/* reexport safe */ _chunk_27HFSL7N_js__WEBPACK_IMPORTED_MODULE_3__.useSidebar),\n/* harmony export */   useTreeContext: () => (/* reexport safe */ _chunk_LSTPTAZ5_js__WEBPACK_IMPORTED_MODULE_0__.useTreeContext)\n/* harmony export */ });\n/* harmony import */ var _chunk_LSTPTAZ5_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-LSTPTAZ5.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-LSTPTAZ5.js\");\n/* harmony import */ var _chunk_UOD2T27N_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-UOD2T27N.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-UOD2T27N.js\");\n/* harmony import */ var _chunk_ET4TW6M5_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-ET4TW6M5.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-ET4TW6M5.js\");\n/* harmony import */ var _chunk_27HFSL7N_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-27HFSL7N.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-27HFSL7N.js\");\n/* harmony import */ var _chunk_EFMHXXHW_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-EFMHXXHW.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-EFMHXXHW.js\");\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/chunk-MLKGABMK.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ RootProvider,useI18n,useSearchContext,useSidebar,useTreeContext auto */ \n\n\n\n\n\n// src/provider.tsx\n\n\n\n\nvar DefaultSearchDialog = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(async ()=>{\n    typeof require.resolveWeak !== \"undefined\" && require.resolveWeak(\"./components/dialog/search-default.js\");\n}, {\n    loadableGenerated: {\n        modules: [\n            \"node_modules\\\\fumadocs-ui\\\\dist\\\\provider.js -> \" + \"./components/dialog/search-default.js\"\n        ]\n    },\n    ssr: false\n});\nfunction RootProvider({ children, dir, theme: { enabled = true, ...theme } = {}, search }) {\n    let body = children;\n    if (search?.enabled !== false) body = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_chunk_ET4TW6M5_js__WEBPACK_IMPORTED_MODULE_2__.SearchProvider, {\n        SearchDialog: DefaultSearchDialog,\n        ...search,\n        children: body\n    });\n    if (enabled) body = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(next_themes__WEBPACK_IMPORTED_MODULE_6__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        ...theme,\n        children: body\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_9__.DirectionProvider, {\n        dir: dir ?? \"ltr\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_chunk_27HFSL7N_js__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n            children: body\n        })\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-ui/dist/provider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-ui/dist/provider.js":
/*!***************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/provider.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RootProvider: () => (/* binding */ e0),
/* harmony export */   useI18n: () => (/* binding */ e1),
/* harmony export */   useSearchContext: () => (/* binding */ e2),
/* harmony export */   useSidebar: () => (/* binding */ e3),
/* harmony export */   useTreeContext: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\fumadocs-static-search\node_modules\fumadocs-ui\dist\provider.js#RootProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\fumadocs-static-search\node_modules\fumadocs-ui\dist\provider.js#useI18n`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\fumadocs-static-search\node_modules\fumadocs-ui\dist\provider.js#useSearchContext`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\fumadocs-static-search\node_modules\fumadocs-ui\dist\provider.js#useSidebar`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\fumadocs-static-search\node_modules\fumadocs-ui\dist\provider.js#useTreeContext`);


/***/ })

};
;