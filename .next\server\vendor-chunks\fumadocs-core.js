"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-core";
exports.ids = ["vendor-chunks/fumadocs-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-KGMG4N3Y.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-KGMG4N3Y.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* binding */ useOnChange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-on-change.ts\n\nfunction useOnChange(value, onChange) {\n  const [prev, setPrev] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n  if (prev !== value) {\n    onChange(value, prev);\n    setPrev(value);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtHTUc0TjNZLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDaUM7QUFDakM7QUFDQSwwQkFBMEIsK0NBQVE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFJRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1bWFkb2NzLXN0YXRpYy1zZWFyY2gvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtHTUc0TjNZLmpzPzlhODkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzL3VzZS1vbi1jaGFuZ2UudHNcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VPbkNoYW5nZSh2YWx1ZSwgb25DaGFuZ2UpIHtcbiAgY29uc3QgW3ByZXYsIHNldFByZXZdID0gdXNlU3RhdGUodmFsdWUpO1xuICBpZiAocHJldiAhPT0gdmFsdWUpIHtcbiAgICBvbkNoYW5nZSh2YWx1ZSwgcHJldik7XG4gICAgc2V0UHJldih2YWx1ZSk7XG4gIH1cbn1cblxuZXhwb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-KGMG4N3Y.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __export: () => (/* binding */ __export)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLU1MS0dBQk1LLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsa0NBQWtDO0FBQ2hFOztBQUlFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnVtYWRvY3Mtc3RhdGljLXNlYXJjaC8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstTUxLR0FCTUsuanM/ODU3NiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19kZWZQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZXhwb3J0ID0gKHRhcmdldCwgYWxsKSA9PiB7XG4gIGZvciAodmFyIG5hbWUgaW4gYWxsKVxuICAgIF9fZGVmUHJvcCh0YXJnZXQsIG5hbWUsIHsgZ2V0OiBhbGxbbmFtZV0sIGVudW1lcmFibGU6IHRydWUgfSk7XG59O1xuXG5leHBvcnQge1xuICBfX2V4cG9ydFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/sidebar.js":
/*!****************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/sidebar.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarList: () => (/* binding */ SidebarList),\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger)\n/* harmony export */ });\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ SidebarList,SidebarProvider,SidebarTrigger auto */ \n// src/sidebar.tsx\n\n\n\nvar SidebarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(void 0);\nfunction useSidebarContext() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SidebarContext);\n    if (!ctx) throw new Error(\"Missing sidebar provider\");\n    return ctx;\n}\nfunction SidebarProvider(props) {\n    const [openInner, setOpenInner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [open, setOpen] = [\n        props.open ?? openInner,\n        props.onOpenChange ?? setOpenInner\n    ];\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SidebarContext.Provider, {\n        value: [\n            open,\n            setOpen\n        ],\n        children: props.children\n    });\n}\nfunction SidebarTrigger({ as, ...props }) {\n    const [open, setOpen] = useSidebarContext();\n    const As = as ?? \"button\";\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(As, {\n        \"aria-label\": \"Toggle Sidebar\",\n        \"data-open\": open,\n        onClick: (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n            setOpen(!open);\n        }, [\n            open,\n            setOpen\n        ]),\n        ...props\n    });\n}\nfunction SidebarList({ as, blockScrollingWidth, ...props }) {\n    const [open] = useSidebarContext();\n    const [isBlocking, setIsBlocking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!blockScrollingWidth) return;\n        const mediaQueryList = window.matchMedia(`(min-width: ${blockScrollingWidth.toString()}px)`);\n        const handleChange = ()=>{\n            setIsBlocking(!mediaQueryList.matches);\n        };\n        handleChange();\n        mediaQueryList.addEventListener(\"change\", handleChange);\n        return ()=>{\n            mediaQueryList.removeEventListener(\"change\", handleChange);\n        };\n    }, [\n        blockScrollingWidth\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        as: as ?? \"aside\",\n        \"data-open\": open,\n        enabled: Boolean(isBlocking && open),\n        ...props,\n        children: props.children\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/sidebar.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js":
/*!****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-on-change.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* reexport safe */ _chunk_KGMG4N3Y_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)\n/* harmony export */ });\n/* harmony import */ var _chunk_KGMG4N3Y_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-KGMG4N3Y.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KGMG4N3Y.js\");\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-MLKGABMK.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1vbi1jaGFuZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRThCO0FBQ0E7QUFHNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mdW1hZG9jcy1zdGF0aWMtc2VhcmNoLy4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2Utb24tY2hhbmdlLmpzPzcwZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn0gZnJvbSBcIi4uL2NodW5rLUtHTUc0TjNZLmpzXCI7XG5pbXBvcnQgXCIuLi9jaHVuay1NTEtHQUJNSy5qc1wiO1xuZXhwb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js\n");

/***/ })

};
;