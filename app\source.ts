// 简化的文档源配置
export const pages = [
  {
    url: '/docs',
    slugs: [],
    data: {
      title: '文档首页',
      description: '欢迎来到Fumadocs静态搜索文档',
      body: () => import('@/content/docs/index.mdx'),
    },
  },
  {
    url: '/docs/getting-started',
    slugs: ['getting-started'],
    data: {
      title: '快速开始',
      description: '学习如何快速开始使用Fumadocs',
      body: () => import('@/content/docs/getting-started.mdx'),
    },
  },
  {
    url: '/docs/installation',
    slugs: ['installation'],
    data: {
      title: '安装指南',
      description: '详细的安装和配置指南',
      body: () => import('@/content/docs/installation.mdx'),
    },
  },
  {
    url: '/docs/configuration',
    slugs: ['configuration'],
    data: {
      title: '配置选项',
      description: '了解Fumadocs的各种配置选项',
      body: () => import('@/content/docs/configuration.mdx'),
    },
  },
  {
    url: '/docs/static-export',
    slugs: ['static-export'],
    data: {
      title: '静态导出',
      description: '学习如何将文档站点导出为静态文件',
      body: () => import('@/content/docs/static-export.mdx'),
    },
  },
  {
    url: '/docs/search',
    slugs: ['search'],
    data: {
      title: '搜索功能',
      description: '了解如何使用和自定义搜索功能',
      body: () => import('@/content/docs/search.mdx'),
    },
  },
];

export function getPage(slugs?: string[]) {
  const slug = slugs?.join('/') || '';
  const url = slug ? `/docs/${slug}` : '/docs';
  return pages.find(page => page.url === url);
}

export function getPages() {
  return pages;
}

export const pageTree = {
  name: '文档',
  children: [
    {
      type: 'page',
      name: '文档首页',
      url: '/docs',
    },
    {
      type: 'page',
      name: '快速开始',
      url: '/docs/getting-started',
    },
    {
      type: 'page',
      name: '安装指南',
      url: '/docs/installation',
    },
    {
      type: 'page',
      name: '配置选项',
      url: '/docs/configuration',
    },
    {
      type: 'page',
      name: '静态导出',
      url: '/docs/static-export',
    },
    {
      type: 'page',
      name: '搜索功能',
      url: '/docs/search',
    },
  ],
};
