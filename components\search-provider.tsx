'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import FlexSearch from 'flexsearch';

interface SearchResult {
  id: string;
  title: string;
  content: string;
  url: string;
}

interface SearchContextType {
  search: (query: string) => SearchResult[];
  isLoading: boolean;
}

const SearchContext = createContext<SearchContextType | null>(null);

export function SearchProvider({ children }: { children: React.ReactNode }) {
  const [index, setIndex] = useState<FlexSearch.Index | null>(null);
  const [documents, setDocuments] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeSearch = async () => {
      try {
        // 创建搜索索引
        const searchIndex = new FlexSearch.Index({
          tokenize: 'forward',
          cache: true,
        });

        // 文档数据
        const docs: SearchResult[] = [
          {
            id: '1',
            title: '文档首页',
            content: '欢迎来到Fumadocs静态搜索文档。这是一个支持静态导出和本地搜索功能的文档站点。',
            url: '/docs',
          },
          {
            id: '2',
            title: '快速开始',
            content: '学习如何快速开始使用Fumadocs。包括安装、配置和基本使用方法。',
            url: '/docs/getting-started',
          },
          {
            id: '3',
            title: '安装指南',
            content: '详细的安装和配置指南。学习如何安装和配置Fumadocs。包括依赖安装、配置文件设置等步骤。',
            url: '/docs/installation',
          },
          {
            id: '4',
            title: '配置选项',
            content: '了解Fumadocs的各种配置选项，包括主题、搜索、导航等设置。',
            url: '/docs/configuration',
          },
          {
            id: '5',
            title: '静态导出',
            content: '学习如何将文档站点导出为静态文件，以便部署到任何静态托管服务。',
            url: '/docs/static-export',
          },
          {
            id: '6',
            title: '搜索功能',
            content: '了解如何使用和自定义搜索功能。基于FlexSearch的客户端搜索实现。',
            url: '/docs/search',
          },
        ];

        // 将文档添加到索引
        docs.forEach((doc) => {
          searchIndex.add(doc.id, `${doc.title} ${doc.content}`);
        });

        setIndex(searchIndex);
        setDocuments(docs);
        setIsLoading(false);
      } catch (error) {
        console.error('搜索初始化失败:', error);
        setIsLoading(false);
      }
    };

    initializeSearch();
  }, []);

  const search = (query: string): SearchResult[] => {
    if (!index || !query.trim()) {
      return [];
    }

    try {
      const results = index.search(query, { limit: 10 });
      return results
        .map((id) => documents.find((doc) => doc.id === id))
        .filter((doc): doc is SearchResult => doc !== undefined);
    } catch (error) {
      console.error('搜索失败:', error);
      return [];
    }
  };

  return (
    <SearchContext.Provider value={{ search, isLoading }}>
      {children}
    </SearchContext.Provider>
  );
}

export function useSearch() {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
}
