import type { ReactNode } from 'react';
import { SearchProvider } from '@/components/search-provider';
import { SearchDialog } from '@/components/search-dialog';
import Link from 'next/link';
import { getPages } from '@/app/source';

export default function RootDocsLayout({
  children,
}: {
  children: ReactNode;
}) {
  const pages = getPages();

  return (
    <SearchProvider>
      <div className="min-h-screen bg-gray-50">
        {/* 导航栏 */}
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <Link href="/docs" className="text-xl font-bold text-gray-900">
                  Fumadocs 文档
                </Link>
              </div>
              <div className="flex items-center">
                <SearchDialog />
              </div>
            </div>
          </div>
        </nav>

        <div className="flex">
          {/* 侧边栏 */}
          <aside className="w-64 bg-white shadow-sm min-h-screen">
            <nav className="p-4">
              <ul className="space-y-2">
                {pages.map((page) => (
                  <li key={page.url}>
                    <Link
                      href={page.url}
                      className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                    >
                      {page.data.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          </aside>

          {/* 主内容区 */}
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </SearchProvider>
  );
}
